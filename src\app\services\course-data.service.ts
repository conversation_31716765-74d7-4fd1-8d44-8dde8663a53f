import { Injectable, signal } from '@angular/core';
import { 
  CourseStructure, 
  Topic, 
  ContentPage, 
  AudioPoint, 
  AppState, 
  Highlight, 
  Bookmark, 
  UserNote,
  TextSizeOption,
  HighlightColor 
} from '../models/course.models';

@Injectable({
  providedIn: 'root'
})
export class CourseDataService {
  
  // App state signal
  appState = signal<AppState>({
    selectedTopicId: null,
    selectedPageId: null,
    currentPageNumber: 1,
    textSize: 'normal',
    selectedHighlightColor: '#ffff00',
    highlights: [],
    bookmarks: [],
    notes: [],
    isAudioPlaying: false,
    currentAudioId: null,
    audioCurrentTime: 0
  });

  // Text size options
  textSizeOptions: TextSizeOption[] = [
    { label: 'Small', value: 'small', className: 'text-sm' },
    { label: 'Normal', value: 'normal', className: 'text-base' },
    { label: 'Large', value: 'large', className: 'text-lg' }
  ];

  // Highlight colors
  highlightColors: HighlightColor[] = [
    { name: 'Yellow', value: '#ffff00', class: 'bg-yellow-300' },
    { name: 'Green', value: '#90EE90', class: 'bg-green-300' },
    { name: 'Blue', value: '#87CEEB', class: 'bg-blue-300' },
    { name: 'Pink', value: '#FFB6C1', class: 'bg-pink-300' },
    { name: 'Orange', value: '#FFA500', class: 'bg-orange-300' },
    { name: 'Purple', value: '#DDA0DD', class: 'bg-purple-300' }
  ];

  // Mock course data
  private courseData: CourseStructure = {
    id: 'course-1',
    title: 'Physics - Dynamics',
    topics: [
      {
        id: 'dynamics',
        title: 'Dynamics',
        isExpanded: true,
        pages: [],
        subtopics: [
          {
            id: 'rectilinear-motion',
            title: 'Rectilinear motion (1 of 4)',
            parentId: 'dynamics',
            pages: [
              {
                id: 'page-1',
                pageNumber: 1,
                title: 'Kinematics of Particle',
                content: `
                  <h2>Kinematics of Particle</h2>
                  <p>It is the study of bodies in <strong>motion</strong>, it is divided into two parts</p>
                  
                  <h3>1. Kinematics (Study of geometry of motion):</h3>
                  <p>It is the study of relation between <span class="highlight-term">displacement [s]</span>, <span class="highlight-term">velocity [v]</span>, <span class="highlight-term">acceleration [a]</span> and <span class="highlight-term">time [t]</span>.</p>
                  
                  <h3>2. Kinetics:</h3>
                  <p>It is the study of relation between <span class="highlight-term">force [f]</span>, <span class="highlight-term">mass [m]</span>, <span class="highlight-term">displacement [s]</span>, <span class="highlight-term">velocity [v]</span>, <span class="highlight-term">acceleration [a]</span> and <span class="highlight-term">time [t]</span>.</p>
                  
                  <p>Types of motion based on geometry:</p>
                  
                  <h3>1. Translation:</h3>
                  <p>During the motion of translation, orientation of a body does not change. Translation is of two types:</p>
                  <ul>
                    <li><strong>Rectilinear translation.</strong></li>
                    <li><strong>Curvilinear translation.</strong></li>
                  </ul>
                `,
                audioPoints: [
                  {
                    id: 'audio-1-1',
                    title: 'Introduction to Kinematics',
                    audioUrl: '/assets/audio/kinematics-intro.mp3',
                    duration: 45,
                    startTime: 0,
                    endTime: 30
                  },
                  {
                    id: 'audio-1-2',
                    title: 'Types of Motion',
                    audioUrl: '/assets/audio/types-motion.mp3',
                    duration: 60,
                    startTime: 30,
                    endTime: 90
                  }
                ],
                mainAudio: '/assets/audio/rectilinear-motion.mp3'
              }
            ],
            subtopics: []
          },
          {
            id: 'projectile-motion',
            title: 'Projectile motion',
            parentId: 'dynamics',
            pages: [
              {
                id: 'page-2',
                pageNumber: 1,
                title: 'Projectile Motion Basics',
                content: `
                  <h2>Projectile Motion</h2>
                  <p>Motion in two dimensions under the influence of gravity.</p>
                  
                  <h3>Key Concepts:</h3>
                  <ul>
                    <li>Horizontal velocity remains constant</li>
                    <li>Vertical motion follows kinematic equations</li>
                    <li>Trajectory is parabolic</li>
                  </ul>
                `,
                audioPoints: [
                  {
                    id: 'audio-2-1',
                    title: 'Projectile Motion Introduction',
                    audioUrl: '/assets/audio/projectile-intro.mp3',
                    duration: 120,
                    startTime: 0,
                    endTime: 60
                  }
                ]
              }
            ],
            subtopics: []
          }
        ]
      }
    ]
  };

  getCourseData(): CourseStructure {
    return this.courseData;
  }

  updateAppState(updates: Partial<AppState>): void {
    this.appState.update(state => ({ ...state, ...updates }));
  }

  // Topic and page navigation
  selectTopic(topicId: string): void {
    const topic = this.findTopicById(topicId);
    if (topic && topic.pages.length > 0) {
      this.updateAppState({
        selectedTopicId: topicId,
        selectedPageId: topic.pages[0].id,
        currentPageNumber: 1
      });
    }
  }

  selectPage(pageId: string): void {
    const page = this.findPageById(pageId);
    if (page) {
      this.updateAppState({
        selectedPageId: pageId,
        currentPageNumber: page.pageNumber
      });
    }
  }

  // Helper methods
  findTopicById(id: string): Topic | null {
    const findInTopics = (topics: Topic[]): Topic | null => {
      for (const topic of topics) {
        if (topic.id === id) return topic;
        if (topic.subtopics) {
          const found = findInTopics(topic.subtopics);
          if (found) return found;
        }
      }
      return null;
    };
    return findInTopics(this.courseData.topics);
  }

  findPageById(pageId: string): ContentPage | null {
    const findInTopics = (topics: Topic[]): ContentPage | null => {
      for (const topic of topics) {
        const page = topic.pages.find(p => p.id === pageId);
        if (page) return page;
        if (topic.subtopics) {
          const found = findInTopics(topic.subtopics);
          if (found) return found;
        }
      }
      return null;
    };
    return findInTopics(this.courseData.topics);
  }

  getCurrentTopic(): Topic | null {
    const state = this.appState();
    return state.selectedTopicId ? this.findTopicById(state.selectedTopicId) : null;
  }

  getCurrentPage(): ContentPage | null {
    const state = this.appState();
    return state.selectedPageId ? this.findPageById(state.selectedPageId) : null;
  }

  // Highlight management
  addHighlight(highlight: Omit<Highlight, 'id' | 'timestamp'>): void {
    const newHighlight: Highlight = {
      ...highlight,
      id: this.generateId(),
      timestamp: new Date()
    };
    
    this.updateAppState({
      highlights: [...this.appState().highlights, newHighlight]
    });
  }

  removeHighlight(highlightId: string): void {
    this.updateAppState({
      highlights: this.appState().highlights.filter(h => h.id !== highlightId)
    });
  }

  // Bookmark management
  addBookmark(bookmark: Omit<Bookmark, 'id' | 'timestamp'>): void {
    const newBookmark: Bookmark = {
      ...bookmark,
      id: this.generateId(),
      timestamp: new Date()
    };
    
    this.updateAppState({
      bookmarks: [...this.appState().bookmarks, newBookmark]
    });
  }

  removeBookmark(bookmarkId: string): void {
    this.updateAppState({
      bookmarks: this.appState().bookmarks.filter(b => b.id !== bookmarkId)
    });
  }

  // Notes management
  addNote(note: Omit<UserNote, 'id' | 'timestamp'>): void {
    const newNote: UserNote = {
      ...note,
      id: this.generateId(),
      timestamp: new Date()
    };
    
    this.updateAppState({
      notes: [...this.appState().notes, newNote]
    });
  }

  updateNote(noteId: string, content: string): void {
    this.updateAppState({
      notes: this.appState().notes.map(note => 
        note.id === noteId ? { ...note, content } : note
      )
    });
  }

  removeNote(noteId: string): void {
    this.updateAppState({
      notes: this.appState().notes.filter(n => n.id !== noteId)
    });
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }
}
