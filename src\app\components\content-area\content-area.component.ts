import { Component, computed, inject, ElementRef, ViewChild, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CourseDataService } from '../../services/course-data.service';
import { ContentPage, AudioPoint } from '../../models/course.models';

@Component({
  selector: 'app-content-area',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="content-area">
      @if (currentPage()) {
        <!-- Audio Player Section -->
        <div class="audio-section">
          <div class="audio-player">
            <div class="audio-info">
              <span class="audio-title">{{ currentAudioTitle() }}</span>
              <div class="audio-controls">
                <button 
                  class="play-pause-btn"
                  (click)="toggleAudio()"
                  [class.playing]="appState().isAudioPlaying"
                >
                  {{ appState().isAudioPlaying ? '⏸️' : '▶️' }}
                </button>
                <div class="audio-progress">
                  <div class="progress-bar">
                    <div 
                      class="progress-fill"
                      [style.width.%]="audioProgress()"
                    ></div>
                  </div>
                  <div class="time-display">
                    <span>{{ formatTime(appState().audioCurrentTime) }}</span>
                    <span>-{{ formatTime(currentAudioDuration()) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Content Section -->
        <div class="content-section" [class]="textSizeClass()">
          <div class="content-header">
            <h1>{{ currentPage()?.title }}</h1>
          </div>
          
          <div 
            class="content-body"
            #contentBody
            [innerHTML]="processedContent()"
            (mouseup)="handleTextSelection()"
          ></div>
        </div>
      } @else {
        <div class="no-content">
          <h2>Select a topic to view content</h2>
          <p>Choose a topic from the sidebar to start learning.</p>
        </div>
      }

      <!-- Audio element for playback -->
      <audio 
        #audioPlayer
        (timeupdate)="onTimeUpdate()"
        (ended)="onAudioEnded()"
        (loadedmetadata)="onAudioLoaded()"
      ></audio>
    </div>
  `,
  styles: [`
    .content-area {
      flex: 1;
      height: 100vh;
      overflow-y: auto;
      background-color: #ffffff;
      display: flex;
      flex-direction: column;
    }

    .audio-section {
      background-color: #f8f9fa;
      border-bottom: 1px solid #e9ecef;
      padding: 16px 24px;
      position: sticky;
      top: 0;
      z-index: 10;
    }

    .audio-player {
      max-width: 800px;
      margin: 0 auto;
    }

    .audio-info {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .audio-title {
      font-weight: 600;
      color: #2c3e50;
      min-width: 200px;
    }

    .audio-controls {
      display: flex;
      align-items: center;
      gap: 12px;
      flex: 1;
    }

    .play-pause-btn {
      width: 40px;
      height: 40px;
      border: none;
      border-radius: 50%;
      background-color: #3498db;
      color: white;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      transition: background-color 0.2s;
    }

    .play-pause-btn:hover {
      background-color: #2980b9;
    }

    .play-pause-btn.playing {
      background-color: #e74c3c;
    }

    .play-pause-btn.playing:hover {
      background-color: #c0392b;
    }

    .audio-progress {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .progress-bar {
      flex: 1;
      height: 6px;
      background-color: #e9ecef;
      border-radius: 3px;
      overflow: hidden;
      cursor: pointer;
    }

    .progress-fill {
      height: 100%;
      background-color: #3498db;
      transition: width 0.1s;
    }

    .time-display {
      display: flex;
      gap: 4px;
      font-size: 12px;
      color: #6c757d;
      min-width: 80px;
    }

    .content-section {
      flex: 1;
      padding: 24px;
      max-width: 800px;
      margin: 0 auto;
      width: 100%;
    }

    .content-section.text-sm {
      font-size: 14px;
    }

    .content-section.text-base {
      font-size: 16px;
    }

    .content-section.text-lg {
      font-size: 18px;
    }

    .content-header h1 {
      color: #2c3e50;
      font-size: 28px;
      font-weight: 700;
      margin-bottom: 24px;
      border-bottom: 2px solid #3498db;
      padding-bottom: 12px;
    }

    .content-body {
      line-height: 1.6;
      color: #2c3e50;
    }

    .content-body h2 {
      color: #2c3e50;
      font-size: 22px;
      font-weight: 600;
      margin: 24px 0 16px 0;
    }

    .content-body h3 {
      color: #34495e;
      font-size: 18px;
      font-weight: 600;
      margin: 20px 0 12px 0;
    }

    .content-body p {
      margin-bottom: 16px;
    }

    .content-body ul, .content-body ol {
      margin: 16px 0;
      padding-left: 24px;
    }

    .content-body li {
      margin-bottom: 8px;
    }

    .content-body strong {
      font-weight: 600;
      color: #2c3e50;
    }

    .content-body .highlight-term {
      background-color: #fff3cd;
      padding: 2px 4px;
      border-radius: 3px;
      font-weight: 500;
    }

    .no-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      color: #6c757d;
    }

    .no-content h2 {
      margin-bottom: 12px;
      color: #495057;
    }

    /* Highlight styles */
    .user-highlight {
      padding: 2px 4px;
      border-radius: 3px;
      cursor: pointer;
    }

    .highlight-yellow { background-color: #ffff00; }
    .highlight-green { background-color: #90EE90; }
    .highlight-blue { background-color: #87CEEB; }
    .highlight-pink { background-color: #FFB6C1; }
    .highlight-orange { background-color: #FFA500; }
    .highlight-purple { background-color: #DDA0DD; }

    /* Selection highlight */
    ::selection {
      background-color: rgba(52, 152, 219, 0.3);
    }

    /* Scrollbar styling */
    .content-area::-webkit-scrollbar {
      width: 8px;
    }

    .content-area::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    .content-area::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;
    }

    .content-area::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
  `]
})
export class ContentAreaComponent implements AfterViewInit {
  @ViewChild('audioPlayer') audioPlayer!: ElementRef<HTMLAudioElement>;
  @ViewChild('contentBody') contentBody!: ElementRef<HTMLDivElement>;

  private courseDataService = inject(CourseDataService);
  
  appState = computed(() => this.courseDataService.appState());
  currentPage = computed(() => this.courseDataService.getCurrentPage());
  
  textSizeClass = computed(() => {
    const textSize = this.appState().textSize;
    return `text-${textSize}`;
  });

  currentAudioTitle = computed(() => {
    const page = this.currentPage();
    if (!page) return 'No audio available';
    
    const currentAudioId = this.appState().currentAudioId;
    if (currentAudioId) {
      const audioPoint = page.audioPoints.find(ap => ap.id === currentAudioId);
      if (audioPoint) return audioPoint.title;
    }
    
    return page.title || 'Main Audio';
  });

  currentAudioDuration = computed(() => {
    const page = this.currentPage();
    if (!page) return 0;
    
    const currentAudioId = this.appState().currentAudioId;
    if (currentAudioId) {
      const audioPoint = page.audioPoints.find(ap => ap.id === currentAudioId);
      if (audioPoint) return audioPoint.duration;
    }
    
    return 180; // Default duration for main audio
  });

  audioProgress = computed(() => {
    const duration = this.currentAudioDuration();
    const currentTime = this.appState().audioCurrentTime;
    return duration > 0 ? (currentTime / duration) * 100 : 0;
  });

  processedContent = computed(() => {
    const page = this.currentPage();
    if (!page) return '';
    
    let content = page.content;
    const highlights = this.appState().highlights.filter(h => h.pageId === page.id);
    
    // Apply highlights to content
    highlights.forEach(highlight => {
      const highlightClass = this.getHighlightClass(highlight.color);
      content = content.replace(
        highlight.text,
        `<span class="user-highlight ${highlightClass}" data-highlight-id="${highlight.id}">${highlight.text}</span>`
      );
    });
    
    return content;
  });

  ngAfterViewInit(): void {
    // Initialize audio player when page changes
    this.loadCurrentAudio();
  }

  toggleAudio(): void {
    const audio = this.audioPlayer.nativeElement;
    const isPlaying = this.appState().isAudioPlaying;
    
    if (isPlaying) {
      audio.pause();
    } else {
      audio.play();
    }
    
    this.courseDataService.updateAppState({ isAudioPlaying: !isPlaying });
  }

  onTimeUpdate(): void {
    const currentTime = this.audioPlayer.nativeElement.currentTime;
    this.courseDataService.updateAppState({ audioCurrentTime: currentTime });
  }

  onAudioEnded(): void {
    this.courseDataService.updateAppState({ 
      isAudioPlaying: false,
      audioCurrentTime: 0 
    });
  }

  onAudioLoaded(): void {
    // Audio metadata loaded
  }

  handleTextSelection(): void {
    const selection = window.getSelection();
    if (selection && selection.toString().trim().length > 0) {
      const selectedText = selection.toString().trim();
      const range = selection.getRangeAt(0);
      
      // Store selection for highlighting
      this.storeTextSelection(selectedText, range);
    }
  }

  private storeTextSelection(text: string, range: Range): void {
    // This will be used by the right panel to create highlights
    const event = new CustomEvent('textSelected', {
      detail: { text, range }
    });
    document.dispatchEvent(event);
  }

  private loadCurrentAudio(): void {
    const page = this.currentPage();
    if (!page) return;
    
    const audio = this.audioPlayer.nativeElement;
    const currentAudioId = this.appState().currentAudioId;
    
    let audioUrl = '';
    if (currentAudioId) {
      const audioPoint = page.audioPoints.find(ap => ap.id === currentAudioId);
      audioUrl = audioPoint?.audioUrl || '';
    } else if (page.mainAudio) {
      audioUrl = page.mainAudio;
    }
    
    if (audioUrl && audio.src !== audioUrl) {
      audio.src = audioUrl;
      audio.load();
    }
  }

  private getHighlightClass(color: string): string {
    const colorMap: { [key: string]: string } = {
      '#ffff00': 'highlight-yellow',
      '#90EE90': 'highlight-green',
      '#87CEEB': 'highlight-blue',
      '#FFB6C1': 'highlight-pink',
      '#FFA500': 'highlight-orange',
      '#DDA0DD': 'highlight-purple'
    };
    return colorMap[color] || 'highlight-yellow';
  }

  formatTime(seconds: number): string {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }
}
