import { Component, computed, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CourseDataService } from '../../services/course-data.service';
import { Topic } from '../../models/course.models';

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="sidebar">
      <div class="sidebar-header">
        <h3>Course Structure</h3>
      </div>
      
      <div class="course-content">
        <div class="course-title">{{ courseData().title }}</div>
        
        <div class="topics-list">
          @for (topic of courseData().topics; track topic.id) {
            <div class="topic-item">
              <div 
                class="topic-header"
                (click)="toggleTopic(topic)"
                [class.expanded]="topic.isExpanded"
              >
                <span class="expand-icon">
                  {{ topic.isExpanded ? '▼' : '▶' }}
                </span>
                <span class="topic-title">{{ topic.title }}</span>
              </div>
              
              @if (topic.isExpanded && topic.subtopics) {
                <div class="subtopics">
                  @for (subtopic of topic.subtopics; track subtopic.id) {
                    <div class="subtopic-item">
                      <div 
                        class="subtopic-header"
                        (click)="selectSubtopic(subtopic)"
                        [class.selected]="isTopicSelected(subtopic.id)"
                      >
                        <span class="subtopic-title">{{ subtopic.title }}</span>
                        @if (subtopic.pages.length > 1) {
                          <span class="page-indicator">
                            {{ getCurrentPageNumber(subtopic.id) }}/{{ subtopic.pages.length }}
                          </span>
                        }
                      </div>
                      
                      @if (subtopic.subtopics && subtopic.subtopics.length > 0) {
                        <div class="nested-subtopics">
                          @for (nestedTopic of subtopic.subtopics; track nestedTopic.id) {
                            <div 
                              class="nested-subtopic-item"
                              (click)="selectSubtopic(nestedTopic)"
                              [class.selected]="isTopicSelected(nestedTopic.id)"
                            >
                              <span class="nested-subtopic-title">{{ nestedTopic.title }}</span>
                              @if (nestedTopic.pages.length > 1) {
                                <span class="page-indicator">
                                  {{ getCurrentPageNumber(nestedTopic.id) }}/{{ nestedTopic.pages.length }}
                                </span>
                              }
                            </div>
                          }
                        </div>
                      }
                    </div>
                  }
                </div>
              }
            </div>
          }
        </div>
      </div>
    </div>
  `,
  styles: [`
    .sidebar {
      width: 300px;
      height: 100vh;
      background-color: #2c3e50;
      color: white;
      overflow-y: auto;
      border-right: 1px solid #34495e;
    }

    .sidebar-header {
      padding: 20px;
      background-color: #34495e;
      border-bottom: 1px solid #4a5f7a;
    }

    .sidebar-header h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }

    .course-content {
      padding: 20px;
    }

    .course-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 20px;
      color: #ecf0f1;
    }

    .topics-list {
      space-y: 8px;
    }

    .topic-item {
      margin-bottom: 8px;
    }

    .topic-header {
      display: flex;
      align-items: center;
      padding: 12px;
      background-color: #34495e;
      border-radius: 6px;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    .topic-header:hover {
      background-color: #4a5f7a;
    }

    .topic-header.expanded {
      background-color: #3498db;
    }

    .expand-icon {
      margin-right: 8px;
      font-size: 12px;
      width: 16px;
      text-align: center;
    }

    .topic-title {
      font-weight: 500;
      font-size: 14px;
    }

    .subtopics {
      margin-left: 24px;
      margin-top: 8px;
    }

    .subtopic-item {
      margin-bottom: 4px;
    }

    .subtopic-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 12px;
      background-color: #4a5f7a;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    .subtopic-header:hover {
      background-color: #5a6f8a;
    }

    .subtopic-header.selected {
      background-color: #e74c3c;
      color: white;
    }

    .subtopic-title {
      font-size: 13px;
      font-weight: 400;
    }

    .page-indicator {
      font-size: 11px;
      background-color: rgba(255, 255, 255, 0.2);
      padding: 2px 6px;
      border-radius: 10px;
      font-weight: 500;
    }

    .nested-subtopics {
      margin-left: 16px;
      margin-top: 4px;
    }

    .nested-subtopic-item {
      padding: 8px 12px;
      background-color: #5a6f8a;
      border-radius: 4px;
      cursor: pointer;
      margin-bottom: 2px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      transition: background-color 0.2s;
    }

    .nested-subtopic-item:hover {
      background-color: #6a7f9a;
    }

    .nested-subtopic-item.selected {
      background-color: #e74c3c;
      color: white;
    }

    .nested-subtopic-title {
      font-size: 12px;
      font-weight: 400;
    }

    /* Scrollbar styling */
    .sidebar::-webkit-scrollbar {
      width: 6px;
    }

    .sidebar::-webkit-scrollbar-track {
      background: #2c3e50;
    }

    .sidebar::-webkit-scrollbar-thumb {
      background: #4a5f7a;
      border-radius: 3px;
    }

    .sidebar::-webkit-scrollbar-thumb:hover {
      background: #5a6f8a;
    }
  `]
})
export class SidebarComponent {
  private courseDataService = inject(CourseDataService);
  
  courseData = computed(() => this.courseDataService.getCourseData());
  appState = computed(() => this.courseDataService.appState());

  toggleTopic(topic: Topic): void {
    topic.isExpanded = !topic.isExpanded;
  }

  selectSubtopic(subtopic: Topic): void {
    this.courseDataService.selectTopic(subtopic.id);
  }

  isTopicSelected(topicId: string): boolean {
    return this.appState().selectedTopicId === topicId;
  }

  getCurrentPageNumber(topicId: string): number {
    const state = this.appState();
    if (state.selectedTopicId === topicId) {
      return state.currentPageNumber;
    }
    return 1;
  }
}
