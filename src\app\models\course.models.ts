export interface AudioPoint {
  id: string;
  title: string;
  audioUrl: string;
  duration: number; // in seconds
  startTime?: number; // start time in content
  endTime?: number; // end time in content
}

export interface ContentPage {
  id: string;
  pageNumber: number;
  title: string;
  content: string; // HTML content
  audioPoints: AudioPoint[];
  mainAudio?: string; // main audio for the entire page
}

export interface Topic {
  id: string;
  title: string;
  isExpanded?: boolean;
  pages: ContentPage[];
  subtopics?: Topic[];
  parentId?: string;
}

export interface CourseStructure {
  id: string;
  title: string;
  topics: Topic[];
}

export interface Highlight {
  id: string;
  topicId: string;
  pageId: string;
  text: string;
  color: string;
  startOffset: number;
  endOffset: number;
  timestamp: Date;
  note?: string;
}

export interface Bookmark {
  id: string;
  topicId: string;
  pageId: string;
  title: string;
  timestamp: Date;
  note?: string;
}

export interface UserNote {
  id: string;
  content: string; // HTML content with formatting
  timestamp: Date;
  topicId?: string;
  pageId?: string;
}

export interface AppState {
  selectedTopicId: string | null;
  selectedPageId: string | null;
  currentPageNumber: number;
  textSize: 'small' | 'normal' | 'large';
  selectedHighlightColor: string;
  highlights: Highlight[];
  bookmarks: Bookmark[];
  notes: UserNote[];
  isAudioPlaying: boolean;
  currentAudioId: string | null;
  audioCurrentTime: number;
}

export interface TextSizeOption {
  label: string;
  value: 'small' | 'normal' | 'large';
  className: string;
}

export interface HighlightColor {
  name: string;
  value: string;
  class: string;
}
