import { Component, computed, inject, OnInit, ViewChild, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { CourseDataService } from '../../services/course-data.service';
import { ContentPage, AudioPoint, UserNote } from '../../models/course.models';

@Component({
  selector: 'app-right-panel',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="right-panel">
      <!-- Page Navigation -->
      <div class="panel-section">
        <div class="section-header">
          <span>Page:</span>
          <div class="pagination">
            @for (pageNum of pageNumbers(); track pageNum) {
              <button 
                class="page-btn"
                [class.active]="pageNum === appState().currentPageNumber"
                (click)="goToPage(pageNum)"
              >
                {{ pageNum }}
              </button>
            }
            <button class="nav-btn" (click)="nextPage()" [disabled]="!hasNextPage()">
              ▶
            </button>
          </div>
          <button class="bookmark-btn" (click)="toggleBookmark()">
            {{ isBookmarked() ? '🔖' : '📑' }}
          </button>
        </div>
      </div>

      <!-- Text Size Control -->
      <div class="panel-section">
        <div class="section-title">Text size:</div>
        <div class="text-size-controls">
          @for (option of textSizeOptions; track option.value) {
            <button 
              class="size-btn"
              [class.active]="option.value === appState().textSize"
              (click)="setTextSize(option.value)"
            >
              {{ option.label }}
            </button>
          }
        </div>
      </div>

      <!-- Now Playing -->
      <div class="panel-section">
        <div class="section-title">Now playing:</div>
        <div class="now-playing">
          <div class="current-audio">
            <span class="audio-name">{{ currentAudioTitle() }}</span>
            <div class="audio-controls-mini">
              <button class="control-btn" (click)="previousAudio()">⏮️</button>
              <button class="control-btn" (click)="toggleAudio()">
                {{ appState().isAudioPlaying ? '⏸️' : '▶️' }}
              </button>
              <button class="control-btn" (click)="nextAudio()">⏭️</button>
            </div>
          </div>
        </div>
      </div>

      <!-- Annotate -->
      <div class="panel-section">
        <div class="section-title">Annotate:</div>
        <div class="highlight-colors">
          @for (color of highlightColors; track color.value) {
            <button 
              class="color-btn"
              [style.background-color]="color.value"
              [class.active]="color.value === appState().selectedHighlightColor"
              (click)="selectHighlightColor(color.value)"
              [title]="color.name"
            ></button>
          }
        </div>
      </div>

      <!-- Audio List -->
      <div class="panel-section">
        <div class="section-title">Audio List:</div>
        <div class="audio-list">
          @if (currentPageAudioPoints().length > 0) {
            @for (audio of currentPageAudioPoints(); track audio.id) {
              <div 
                class="audio-item"
                [class.active]="audio.id === appState().currentAudioId"
                (click)="playAudio(audio.id)"
              >
                <span class="audio-title">{{ audio.title }}</span>
                <span class="audio-duration">{{ formatDuration(audio.duration) }}</span>
              </div>
            }
          } @else {
            <div class="no-audio">No audio points available</div>
          }
        </div>
      </div>

      <!-- Notes -->
      <div class="panel-section notes-section">
        <div class="section-title">Notes:</div>
        <div class="notes-editor">
          <div class="editor-toolbar">
            <button class="format-btn" (click)="formatText('bold')" title="Bold">
              <strong>B</strong>
            </button>
            <button class="format-btn" (click)="formatText('underline')" title="Underline">
              <u>U</u>
            </button>
            <button class="format-btn" (click)="formatText('italic')" title="Italic">
              <em>I</em>
            </button>
            <button class="format-btn" (click)="formatText('justifyLeft')" title="Align Left">
              ≡
            </button>
          </div>
          <div 
            class="notes-textarea"
            #notesEditor
            contenteditable="true"
            (input)="onNotesChange()"
            [innerHTML]="currentNoteContent()"
            placeholder="Write your notes here..."
          ></div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .right-panel {
      width: 300px;
      height: 100vh;
      background-color: #f8f9fa;
      border-left: 1px solid #e9ecef;
      overflow-y: auto;
      padding: 16px;
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .panel-section {
      background-color: white;
      border-radius: 8px;
      padding: 16px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;
      font-weight: 600;
      color: #2c3e50;
    }

    .section-title {
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 12px;
      font-size: 14px;
    }

    .pagination {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .page-btn, .nav-btn {
      width: 32px;
      height: 32px;
      border: 1px solid #dee2e6;
      background-color: white;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s;
    }

    .page-btn:hover, .nav-btn:hover {
      background-color: #e9ecef;
    }

    .page-btn.active {
      background-color: #007bff;
      color: white;
      border-color: #007bff;
    }

    .nav-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .bookmark-btn {
      background: none;
      border: none;
      font-size: 20px;
      cursor: pointer;
      padding: 4px;
    }

    .text-size-controls {
      display: flex;
      gap: 8px;
    }

    .size-btn {
      flex: 1;
      padding: 8px 12px;
      border: 1px solid #dee2e6;
      background-color: white;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      transition: all 0.2s;
    }

    .size-btn:hover {
      background-color: #e9ecef;
    }

    .size-btn.active {
      background-color: #007bff;
      color: white;
      border-color: #007bff;
    }

    .now-playing {
      background-color: #f8f9fa;
      border-radius: 6px;
      padding: 12px;
    }

    .current-audio {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .audio-name {
      font-size: 13px;
      font-weight: 500;
      color: #2c3e50;
    }

    .audio-controls-mini {
      display: flex;
      justify-content: center;
      gap: 8px;
    }

    .control-btn {
      width: 32px;
      height: 32px;
      border: none;
      background-color: #007bff;
      color: white;
      border-radius: 50%;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      transition: background-color 0.2s;
    }

    .control-btn:hover {
      background-color: #0056b3;
    }

    .highlight-colors {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .color-btn {
      width: 32px;
      height: 32px;
      border: 2px solid transparent;
      border-radius: 50%;
      cursor: pointer;
      transition: all 0.2s;
    }

    .color-btn:hover {
      transform: scale(1.1);
    }

    .color-btn.active {
      border-color: #2c3e50;
      transform: scale(1.1);
    }

    .audio-list {
      max-height: 200px;
      overflow-y: auto;
    }

    .audio-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.2s;
      margin-bottom: 4px;
    }

    .audio-item:hover {
      background-color: #f8f9fa;
    }

    .audio-item.active {
      background-color: #007bff;
      color: white;
    }

    .audio-title {
      font-size: 12px;
      font-weight: 500;
    }

    .audio-duration {
      font-size: 11px;
      opacity: 0.7;
    }

    .no-audio {
      text-align: center;
      color: #6c757d;
      font-size: 12px;
      padding: 16px;
    }

    .notes-section {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .notes-editor {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .editor-toolbar {
      display: flex;
      gap: 4px;
      margin-bottom: 8px;
      padding: 8px;
      background-color: #f8f9fa;
      border-radius: 4px;
    }

    .format-btn {
      width: 32px;
      height: 32px;
      border: 1px solid #dee2e6;
      background-color: white;
      border-radius: 4px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      transition: all 0.2s;
    }

    .format-btn:hover {
      background-color: #e9ecef;
    }

    .notes-textarea {
      flex: 1;
      min-height: 200px;
      padding: 12px;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      background-color: white;
      font-size: 14px;
      line-height: 1.5;
      outline: none;
      overflow-y: auto;
    }

    .notes-textarea:focus {
      border-color: #007bff;
      box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
    }

    .notes-textarea[contenteditable]:empty::before {
      content: attr(placeholder);
      color: #6c757d;
      font-style: italic;
    }

    /* Scrollbar styling */
    .right-panel::-webkit-scrollbar,
    .audio-list::-webkit-scrollbar,
    .notes-textarea::-webkit-scrollbar {
      width: 6px;
    }

    .right-panel::-webkit-scrollbar-track,
    .audio-list::-webkit-scrollbar-track,
    .notes-textarea::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    .right-panel::-webkit-scrollbar-thumb,
    .audio-list::-webkit-scrollbar-thumb,
    .notes-textarea::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }

    .right-panel::-webkit-scrollbar-thumb:hover,
    .audio-list::-webkit-scrollbar-thumb:hover,
    .notes-textarea::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
  `]
})
export class RightPanelComponent implements OnInit {
  @ViewChild('notesEditor') notesEditor!: ElementRef<HTMLDivElement>;

  private courseDataService = inject(CourseDataService);
  
  appState = computed(() => this.courseDataService.appState());
  currentPage = computed(() => this.courseDataService.getCurrentPage());
  currentTopic = computed(() => this.courseDataService.getCurrentTopic());
  
  textSizeOptions = this.courseDataService.textSizeOptions;
  highlightColors = this.courseDataService.highlightColors;

  pageNumbers = computed(() => {
    const topic = this.currentTopic();
    if (!topic) return [];
    return Array.from({ length: topic.pages.length }, (_, i) => i + 1);
  });

  currentPageAudioPoints = computed(() => {
    const page = this.currentPage();
    return page?.audioPoints || [];
  });

  currentAudioTitle = computed(() => {
    const page = this.currentPage();
    if (!page) return 'No audio';
    
    const currentAudioId = this.appState().currentAudioId;
    if (currentAudioId) {
      const audioPoint = page.audioPoints.find(ap => ap.id === currentAudioId);
      if (audioPoint) return audioPoint.title;
    }
    
    return page.title || 'Main Audio';
  });

  currentNoteContent = computed(() => {
    const state = this.appState();
    const currentNote = state.notes.find(note => 
      note.topicId === state.selectedTopicId && 
      note.pageId === state.selectedPageId
    );
    return currentNote?.content || '';
  });

  ngOnInit(): void {
    // Listen for text selection events from content area
    document.addEventListener('textSelected', this.handleTextSelection.bind(this));
  }

  goToPage(pageNumber: number): void {
    const topic = this.currentTopic();
    if (topic && topic.pages[pageNumber - 1]) {
      this.courseDataService.selectPage(topic.pages[pageNumber - 1].id);
    }
  }

  nextPage(): void {
    const state = this.appState();
    const topic = this.currentTopic();
    if (topic && state.currentPageNumber < topic.pages.length) {
      this.goToPage(state.currentPageNumber + 1);
    }
  }

  hasNextPage(): boolean {
    const state = this.appState();
    const topic = this.currentTopic();
    return topic ? state.currentPageNumber < topic.pages.length : false;
  }

  toggleBookmark(): void {
    const state = this.appState();
    const page = this.currentPage();
    
    if (!state.selectedTopicId || !state.selectedPageId || !page) return;

    const existingBookmark = state.bookmarks.find(b => 
      b.topicId === state.selectedTopicId && b.pageId === state.selectedPageId
    );

    if (existingBookmark) {
      this.courseDataService.removeBookmark(existingBookmark.id);
    } else {
      this.courseDataService.addBookmark({
        topicId: state.selectedTopicId,
        pageId: state.selectedPageId,
        title: page.title
      });
    }
  }

  isBookmarked(): boolean {
    const state = this.appState();
    return state.bookmarks.some(b => 
      b.topicId === state.selectedTopicId && b.pageId === state.selectedPageId
    );
  }

  setTextSize(size: 'small' | 'normal' | 'large'): void {
    this.courseDataService.updateAppState({ textSize: size });
  }

  toggleAudio(): void {
    // This will be handled by the content area component
    const event = new CustomEvent('toggleAudio');
    document.dispatchEvent(event);
  }

  previousAudio(): void {
    const audioPoints = this.currentPageAudioPoints();
    const currentAudioId = this.appState().currentAudioId;
    
    if (audioPoints.length === 0) return;
    
    const currentIndex = audioPoints.findIndex(ap => ap.id === currentAudioId);
    const previousIndex = currentIndex > 0 ? currentIndex - 1 : audioPoints.length - 1;
    
    this.playAudio(audioPoints[previousIndex].id);
  }

  nextAudio(): void {
    const audioPoints = this.currentPageAudioPoints();
    const currentAudioId = this.appState().currentAudioId;
    
    if (audioPoints.length === 0) return;
    
    const currentIndex = audioPoints.findIndex(ap => ap.id === currentAudioId);
    const nextIndex = currentIndex < audioPoints.length - 1 ? currentIndex + 1 : 0;
    
    this.playAudio(audioPoints[nextIndex].id);
  }

  playAudio(audioId: string): void {
    this.courseDataService.updateAppState({ 
      currentAudioId: audioId,
      isAudioPlaying: true,
      audioCurrentTime: 0
    });
    
    // Trigger audio load in content area
    const event = new CustomEvent('playAudio', { detail: { audioId } });
    document.dispatchEvent(event);
  }

  selectHighlightColor(color: string): void {
    this.courseDataService.updateAppState({ selectedHighlightColor: color });
  }

  formatText(command: string): void {
    document.execCommand(command, false);
    this.notesEditor.nativeElement.focus();
  }

  onNotesChange(): void {
    const content = this.notesEditor.nativeElement.innerHTML;
    const state = this.appState();
    
    if (!state.selectedTopicId || !state.selectedPageId) return;

    const existingNote = state.notes.find(note => 
      note.topicId === state.selectedTopicId && 
      note.pageId === state.selectedPageId
    );

    if (existingNote) {
      this.courseDataService.updateNote(existingNote.id, content);
    } else if (content.trim()) {
      this.courseDataService.addNote({
        content,
        topicId: state.selectedTopicId,
        pageId: state.selectedPageId
      });
    }
  }

  private handleTextSelection(event: CustomEvent): void {
    const { text } = event.detail;
    const state = this.appState();
    
    if (!text || !state.selectedTopicId || !state.selectedPageId) return;

    // Create highlight with selected color
    this.courseDataService.addHighlight({
      topicId: state.selectedTopicId,
      pageId: state.selectedPageId,
      text,
      color: state.selectedHighlightColor,
      startOffset: 0, // These would need proper calculation in a real implementation
      endOffset: text.length
    });
  }

  formatDuration(seconds: number): string {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }
}
